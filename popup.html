<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>求职投递进展记录</title>
    <link rel="stylesheet" href="styles/popup.css">
    <link rel="stylesheet" href="styles/table.css">
</head>
<body>
    <div class="container">
        <!-- 顶部工具栏 -->
        <div class="toolbar">
            <div class="toolbar-left">
                <button id="addBtn" class="btn btn-primary" title="添加新记录">
                    <span class="icon">+</span>
                    添加
                </button>
                <button id="exportBtn" class="btn btn-secondary" title="导出表格">
                    <span class="icon">📊</span>
                    导出
                </button>
            </div>
            <div class="toolbar-right">
                <button id="undoBtn" class="btn btn-icon" title="撤销" disabled>
                    <span class="icon">↶</span>
                </button>
                <button id="redoBtn" class="btn btn-icon" title="重做" disabled>
                    <span class="icon">↷</span>
                </button>
            </div>
        </div>

        <!-- 表格容器 -->
        <div class="table-container">
            <table id="applicationsTable" class="applications-table">
                <thead>
                    <tr>
                        <th class="checkbox-col">
                            <input type="checkbox" id="selectAll">
                        </th>
                        <th class="sortable" data-field="company">公司</th>
                        <th class="sortable" data-field="position">职位</th>
                        <th class="sortable" data-field="priority">重视度</th>
                        <th class="sortable" data-field="progress">进展</th>
                        <th class="sortable" data-field="statusProgress">状态</th>
                        <th class="sortable" data-field="applicationTime">投递时间</th>
                        <th class="sortable" data-field="progressTime">进展时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <!-- 表格内容将通过JavaScript动态生成 -->
                </tbody>
            </table>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-toolbar">
            <button id="insertBtn" class="btn btn-secondary" title="在选中位置插入新行" disabled>
                <span class="icon">⬇</span>
                插入
            </button>
            <button id="moveTopBtn" class="btn btn-secondary" title="将选中行移到顶部" disabled>
                <span class="icon">⬆</span>
                置顶
            </button>
            <button id="deleteBtn" class="btn btn-danger" title="删除选中行" disabled>
                <span class="icon">🗑</span>
                删除
            </button>
        </div>
    </div>

    <!-- 导出格式选择模态框 -->
    <div id="exportModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>选择导出格式</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <button id="exportExcel" class="btn btn-primary">导出为 Excel</button>
                <button id="exportCSV" class="btn btn-secondary">导出为 CSV</button>
            </div>
        </div>
    </div>

    <!-- 加载脚本 -->
    <script src="scripts/utils.js"></script>
    <script src="scripts/dataService.js"></script>
    <script src="scripts/tableManager.js"></script>
    <script src="scripts/historyManager.js"></script>
    <script src="scripts/exportManager.js"></script>
    <script src="popup.js"></script>
</body>
</html>
