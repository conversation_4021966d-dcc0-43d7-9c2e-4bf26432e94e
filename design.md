# 设计文档

## 概述

求职投递进展记录插件是一个Edge浏览器扩展，使用Manifest V3规范开发。插件提供一个功能完整的数据管理界面，允许用户记录、跟踪和管理求职申请进展。插件采用现代Web技术栈，包括HTML5、CSS3、JavaScript ES6+，并使用Chrome Extension APIs进行数据存储和浏览器集成。

## 架构

### 整体架构

```mermaid
graph TB
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    C --> D[浏览器存储API]
    
    A --> A1[弹出窗口 popup.html]
    A --> A2[样式文件 styles.css]
    
    B --> B1[应用控制器 app.js]
    B --> B2[表格管理器 tableManager.js]
    B --> B3[操作历史管理器 historyManager.js]
    B --> B4[导出管理器 exportManager.js]
    
    C --> C1[数据服务 dataService.js]
    C --> C2[存储适配器 storageAdapter.js]
    
    D --> D1[chrome.storage.local]
    D --> D2[chrome.storage.sync]
```

### 文件结构

```
job-application-tracker/
├── manifest.json           # 插件配置文件
├── popup.html             # 主界面HTML
├── popup.js               # 主界面逻辑
├── styles/
│   ├── popup.css          # 主样式文件
│   └── table.css          # 表格专用样式
├── scripts/
│   ├── dataService.js     # 数据服务
│   ├── tableManager.js    # 表格管理
│   ├── historyManager.js  # 历史记录管理
│   ├── exportManager.js   # 导出功能
│   └── utils.js           # 工具函数
├── icons/
│   ├── icon16.png         # 16x16图标
│   ├── icon32.png         # 32x32图标
│   ├── icon48.png         # 48x48图标
│   └── icon128.png        # 128x128图标
└── _locales/
    └── zh_CN/
        └── messages.json   # 中文本地化
```

## 组件和接口

### 1. 数据模型

#### JobApplication 数据结构
```javascript
interface JobApplication {
  id: string;                    // 唯一标识符
  company: string;               // 公司名称
  applicationLink: string;       // 投递链接
  priority: 1 | 2 | 3;          // 重视度（1-3级）
  industry: string;              // 行业
  tags: string[];                // 标签数组
  position: string;              // 职位
  location: string;              // 地点
  progress: ProgressStatus;      // 进展状态
  statusProgress: StatusProgress; // 状态进度
  progressTime: Date;            // 进展时间
  applicationTime: Date;         // 投递时间
  notes: string;                 // 备注
  referralCode: string;          // 内推码
  createdAt: Date;               // 创建时间
  updatedAt: Date;               // 更新时间
}

enum ProgressStatus {
  APPLIED = '已投递',
  ASSESSMENT = '测评',
  WRITTEN_TEST = '笔试',
  AI_INTERVIEW = 'AI面',
  FIRST_INTERVIEW = '一面',
  SECOND_INTERVIEW = '二面',
  THIRD_INTERVIEW = '三面',
  HR_FINAL = 'HR终面',
  OFFER_NEGOTIATION = '谈offer',
  SIGNED = '签约'
}

enum StatusProgress {
  WAITING_RESPONSE = '等消息',
  WAITING_MY_REPLY = '等我回复',
  WAITING_START = '等待开始',
  PASSED = '已过',
  FAILED = '未过',
  ABANDONED = '已放弃',
  TRANSFERRED = '被调剂',
  TERMINATED = '解约'
}
```

### 2. 核心组件

#### DataService
```javascript
class DataService {
  async getAllApplications(): Promise<JobApplication[]>
  async saveApplication(app: JobApplication): Promise<void>
  async updateApplication(id: string, updates: Partial<JobApplication>): Promise<void>
  async deleteApplication(id: string): Promise<void>
  async exportData(format: 'excel' | 'csv'): Promise<Blob>
}
```

#### TableManager
```javascript
class TableManager {
  constructor(container: HTMLElement, dataService: DataService)
  render(applications: JobApplication[]): void
  addRow(application?: Partial<JobApplication>): void
  deleteSelectedRows(): void
  moveSelectedToTop(): void
  insertRowAtSelection(): void
  getSelectedRows(): JobApplication[]
  updateRow(id: string, data: Partial<JobApplication>): void
}
```

#### HistoryManager
```javascript
class HistoryManager {
  constructor(maxHistorySize: number = 50)
  recordAction(action: Action): void
  undo(): Action | null
  redo(): Action | null
  canUndo(): boolean
  canRedo(): boolean
  clear(): void
}

interface Action {
  type: 'add' | 'update' | 'delete' | 'move' | 'insert'
  data: any
  timestamp: Date
  reverse(): Action
}
```

#### ExportManager
```javascript
class ExportManager {
  exportToExcel(applications: JobApplication[]): Promise<Blob>
  exportToCSV(applications: JobApplication[]): Promise<Blob>
  downloadFile(blob: Blob, filename: string): void
}
```

### 3. 用户界面组件

#### 主界面布局
- 顶部工具栏：包含添加、导出、撤销、重做按钮
- 表格区域：显示所有投递记录
- 底部操作栏：包含行操作按钮（插入、置顶、删除）

#### 表格设计
- 可排序的列标题
- 行选择复选框
- 内联编辑功能
- 重视度颜色标识
- 下拉选择器（进展、状态）

## 数据模型

### 存储策略

1. **本地存储**：使用 `chrome.storage.local` 存储主要数据
2. **同步存储**：使用 `chrome.storage.sync` 存储用户偏好设置
3. **数据结构**：
   ```javascript
   {
     applications: JobApplication[],
     settings: {
       priorityColors: { 1: '#ff4444', 2: '#ffaa00', 3: '#44ff44' },
       defaultView: 'table',
       autoSave: true
     },
     metadata: {
       version: '1.0.0',
       lastBackup: Date,
       totalRecords: number
     }
   }
   ```

### 数据验证

- 必填字段验证：公司、职位、投递时间
- 数据类型验证：日期格式、URL格式
- 数据长度限制：防止存储溢出
- 重复检查：基于公司+职位+投递时间

## 错误处理

### 错误类型和处理策略

1. **存储错误**
   - 存储空间不足：提示用户清理数据或导出备份
   - 存储权限错误：引导用户检查浏览器设置

2. **数据验证错误**
   - 必填字段缺失：高亮显示错误字段
   - 格式错误：提供格式示例和自动修正建议

3. **导出错误**
   - 文件生成失败：提供重试选项
   - 下载失败：提供手动保存选项

4. **网络错误**
   - 同步失败：提供离线模式提示
   - 链接验证失败：标记无效链接

### 错误处理机制

```javascript
class ErrorHandler {
  static handle(error: Error, context: string): void {
    console.error(`[${context}] ${error.message}`, error);
    
    switch (error.type) {
      case 'STORAGE_ERROR':
        this.showStorageErrorDialog(error);
        break;
      case 'VALIDATION_ERROR':
        this.showValidationError(error);
        break;
      case 'EXPORT_ERROR':
        this.showExportErrorDialog(error);
        break;
      default:
        this.showGenericError(error);
    }
  }
}
```



## 性能优化

### 数据处理优化
- 虚拟滚动：处理大量记录时的性能优化
- 懒加载：按需加载数据
- 数据缓存：减少重复计算

### 用户界面优化
- 防抖处理：输入框实时搜索
- 批量操作：减少DOM操作次数
- CSS优化：使用高效的选择器和动画

### 存储优化
- 数据压缩：减少存储空间占用
- 增量同步：只同步变更的数据
- 定期清理：自动清理过期数据