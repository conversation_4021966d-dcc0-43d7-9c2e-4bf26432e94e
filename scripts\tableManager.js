// 表格管理器类

class TableManager {
    constructor(container, dataService) {
        this.container = container;
        this.dataService = dataService;
        this.table = container.querySelector('#applicationsTable');
        this.tbody = container.querySelector('#tableBody');
        this.selectAllCheckbox = container.querySelector('#selectAll');
        
        this.applications = [];
        this.selectedRows = new Set();
        this.sortField = null;
        this.sortDirection = 'asc';
        this.editingCell = null;
        
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 全选复选框
        this.selectAllCheckbox.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 表头排序
        this.table.querySelectorAll('.sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                const field = e.target.dataset.field;
                this.sortTable(field);
            });
        });

        // 表格点击事件委托
        this.tbody.addEventListener('click', (e) => {
            this.handleTableClick(e);
        });

        // 表格双击事件委托
        this.tbody.addEventListener('dblclick', (e) => {
            this.handleTableDoubleClick(e);
        });

        // 键盘事件
        this.tbody.addEventListener('keydown', (e) => {
            this.handleKeyDown(e);
        });
    }

    /**
     * 渲染表格
     * @param {JobApplication[]} applications 投递记录数组
     */
    render(applications) {
        this.applications = applications || [];
        this.selectedRows.clear();
        this.updateSelectAllCheckbox();
        
        if (this.applications.length === 0) {
            this.renderEmptyState();
            return;
        }

        this.tbody.innerHTML = '';
        
        this.applications.forEach((app, index) => {
            const row = this.createTableRow(app, index);
            this.tbody.appendChild(row);
        });

        this.updateRowSelection();
    }

    /**
     * 创建表格行
     * @param {JobApplication} application 投递记录
     * @param {number} index 行索引
     * @returns {HTMLTableRowElement} 表格行元素
     */
    createTableRow(application, index) {
        const row = document.createElement('tr');
        row.dataset.id = application.id;
        row.dataset.index = index;
        
        // 根据重视度添加CSS类
        row.classList.add(`priority-${application.priority}`);

        row.innerHTML = `
            <td class="checkbox-col">
                <input type="checkbox" class="table-checkbox" data-id="${application.id}">
            </td>
            <td class="editable" data-field="company">${escapeHtml(application.company)}</td>
            <td class="editable" data-field="position">${escapeHtml(application.position)}</td>
            <td class="priority-cell">
                <span class="priority-indicator"></span>
                <select class="custom-select" data-field="priority">
                    <option value="1" ${application.priority === 1 ? 'selected' : ''}>1级</option>
                    <option value="2" ${application.priority === 2 ? 'selected' : ''}>2级</option>
                    <option value="3" ${application.priority === 3 ? 'selected' : ''}>3级</option>
                </select>
            </td>
            <td>
                <select class="custom-select" data-field="progress">
                    ${this.createProgressOptions(application.progress)}
                </select>
            </td>
            <td>
                <select class="custom-select" data-field="statusProgress">
                    ${this.createStatusOptions(application.statusProgress)}
                </select>
            </td>
            <td class="editable" data-field="applicationTime">${formatDate(application.applicationTime)}</td>
            <td class="editable" data-field="progressTime">${formatDate(application.progressTime)}</td>
            <td class="actions-cell">
                <button class="action-btn edit" title="编辑详情">✏️</button>
                <button class="action-btn delete" title="删除记录">🗑️</button>
            </td>
        `;

        // 添加下拉选择器事件监听
        row.querySelectorAll('.custom-select').forEach(select => {
            select.addEventListener('change', (e) => {
                this.handleFieldChange(application.id, e.target.dataset.field, e.target.value);
            });
        });

        return row;
    }

    /**
     * 创建进展状态选项
     * @param {string} currentValue 当前值
     * @returns {string} HTML选项字符串
     */
    createProgressOptions(currentValue) {
        return Object.entries(ProgressStatus).map(([key, value]) => 
            `<option value="${value}" ${value === currentValue ? 'selected' : ''}>${value}</option>`
        ).join('');
    }

    /**
     * 创建状态进度选项
     * @param {string} currentValue 当前值
     * @returns {string} HTML选项字符串
     */
    createStatusOptions(currentValue) {
        return Object.entries(StatusProgress).map(([key, value]) => 
            `<option value="${value}" ${value === currentValue ? 'selected' : ''}>${value}</option>`
        ).join('');
    }

    /**
     * 渲染空状态
     */
    renderEmptyState() {
        this.tbody.innerHTML = `
            <tr>
                <td colspan="9" class="empty-state">
                    <div class="icon">📋</div>
                    <h3>暂无投递记录</h3>
                    <p>点击"添加"按钮创建您的第一条投递记录</p>
                </td>
            </tr>
        `;
    }

    /**
     * 处理表格点击事件
     * @param {Event} e 点击事件
     */
    handleTableClick(e) {
        const target = e.target;
        const row = target.closest('tr');
        
        if (!row || !row.dataset.id) return;

        // 复选框点击
        if (target.classList.contains('table-checkbox')) {
            this.toggleRowSelection(row.dataset.id, target.checked);
            return;
        }

        // 操作按钮点击
        if (target.classList.contains('action-btn')) {
            if (target.classList.contains('edit')) {
                this.editApplication(row.dataset.id);
            } else if (target.classList.contains('delete')) {
                this.deleteApplication(row.dataset.id);
            }
            return;
        }
    }

    /**
     * 处理表格双击事件
     * @param {Event} e 双击事件
     */
    handleTableDoubleClick(e) {
        const target = e.target;
        
        if (target.classList.contains('editable')) {
            this.startInlineEdit(target);
        }
    }

    /**
     * 开始内联编辑
     * @param {HTMLElement} cell 单元格元素
     */
    startInlineEdit(cell) {
        if (this.editingCell) {
            this.finishInlineEdit();
        }

        this.editingCell = cell;
        const field = cell.dataset.field;
        const currentValue = cell.textContent.trim();
        
        cell.classList.add('editing');
        
        if (field === 'applicationTime' || field === 'progressTime') {
            // 日期输入
            cell.innerHTML = `<input type="date" class="edit-input" value="${currentValue}">`;
        } else {
            // 文本输入
            cell.innerHTML = `<input type="text" class="edit-input" value="${escapeHtml(currentValue)}">`;
        }

        const input = cell.querySelector('.edit-input');
        input.focus();
        input.select();

        // 监听输入完成
        input.addEventListener('blur', () => this.finishInlineEdit());
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                this.finishInlineEdit();
            } else if (e.key === 'Escape') {
                this.cancelInlineEdit();
            }
        });
    }

    /**
     * 完成内联编辑
     */
    finishInlineEdit() {
        if (!this.editingCell) return;

        const cell = this.editingCell;
        const input = cell.querySelector('.edit-input');
        const field = cell.dataset.field;
        const newValue = input.value.trim();
        const row = cell.closest('tr');
        const applicationId = row.dataset.id;

        // 更新数据
        this.handleFieldChange(applicationId, field, newValue);

        // 恢复显示
        cell.classList.remove('editing');
        if (field === 'applicationTime' || field === 'progressTime') {
            cell.textContent = formatDate(newValue);
        } else {
            cell.textContent = newValue;
        }

        this.editingCell = null;
    }

    /**
     * 取消内联编辑
     */
    cancelInlineEdit() {
        if (!this.editingCell) return;

        const cell = this.editingCell;
        const row = cell.closest('tr');
        const applicationId = row.dataset.id;
        const application = this.applications.find(app => app.id === applicationId);
        const field = cell.dataset.field;

        // 恢复原值
        cell.classList.remove('editing');
        if (field === 'applicationTime' || field === 'progressTime') {
            cell.textContent = formatDate(application[field]);
        } else {
            cell.textContent = application[field] || '';
        }

        this.editingCell = null;
    }

    /**
     * 处理字段变更
     * @param {string} applicationId 应用ID
     * @param {string} field 字段名
     * @param {any} value 新值
     */
    async handleFieldChange(applicationId, field, value) {
        try {
            const updates = { [field]: value };
            
            // 特殊处理
            if (field === 'priority') {
                updates[field] = parseInt(value);
            } else if (field === 'applicationTime' || field === 'progressTime') {
                updates[field] = parseDate(value) || new Date();
            }

            await this.dataService.updateApplication(applicationId, updates);
            
            // 更新本地数据
            const application = this.applications.find(app => app.id === applicationId);
            if (application) {
                Object.assign(application, updates);
                
                // 如果是重视度变更，更新行样式
                if (field === 'priority') {
                    const row = this.tbody.querySelector(`tr[data-id="${applicationId}"]`);
                    if (row) {
                        row.className = row.className.replace(/priority-\d/, `priority-${value}`);
                    }
                }
            }

            // 触发变更事件
            this.dispatchEvent('dataChanged', { applicationId, field, value });
            
        } catch (error) {
            console.error('更新字段失败:', error);
            showNotification('更新失败: ' + error.message, 'error');
        }
    }

    /**
     * 切换行选择状态
     * @param {string} applicationId 应用ID
     * @param {boolean} selected 是否选中
     */
    toggleRowSelection(applicationId, selected) {
        if (selected) {
            this.selectedRows.add(applicationId);
        } else {
            this.selectedRows.delete(applicationId);
        }

        this.updateRowSelection();
        this.updateSelectAllCheckbox();
        this.dispatchEvent('selectionChanged', { selectedRows: Array.from(this.selectedRows) });
    }

    /**
     * 切换全选状态
     * @param {boolean} selectAll 是否全选
     */
    toggleSelectAll(selectAll) {
        this.selectedRows.clear();
        
        if (selectAll) {
            this.applications.forEach(app => {
                this.selectedRows.add(app.id);
            });
        }

        this.updateRowSelection();
        this.dispatchEvent('selectionChanged', { selectedRows: Array.from(this.selectedRows) });
    }

    /**
     * 更新行选择状态显示
     */
    updateRowSelection() {
        this.tbody.querySelectorAll('tr').forEach(row => {
            const id = row.dataset.id;
            const checkbox = row.querySelector('.table-checkbox');
            const isSelected = this.selectedRows.has(id);
            
            if (checkbox) {
                checkbox.checked = isSelected;
            }
            
            row.classList.toggle('selected', isSelected);
        });
    }

    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox() {
        const totalRows = this.applications.length;
        const selectedCount = this.selectedRows.size;
        
        this.selectAllCheckbox.checked = totalRows > 0 && selectedCount === totalRows;
        this.selectAllCheckbox.indeterminate = selectedCount > 0 && selectedCount < totalRows;
    }

    /**
     * 表格排序
     * @param {string} field 排序字段
     */
    sortTable(field) {
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        // 更新排序指示器
        this.updateSortIndicators();

        // 排序数据
        this.applications.sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];

            // 处理日期类型
            if (field === 'applicationTime' || field === 'progressTime') {
                aVal = new Date(aVal).getTime();
                bVal = new Date(bVal).getTime();
            }
            // 处理数字类型
            else if (field === 'priority') {
                aVal = parseInt(aVal);
                bVal = parseInt(bVal);
            }
            // 处理字符串类型
            else {
                aVal = String(aVal).toLowerCase();
                bVal = String(bVal).toLowerCase();
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        // 重新渲染
        this.render(this.applications);
    }

    /**
     * 更新排序指示器
     */
    updateSortIndicators() {
        this.table.querySelectorAll('.sortable').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
            if (th.dataset.field === this.sortField) {
                th.classList.add(`sort-${this.sortDirection}`);
            }
        });
    }

    /**
     * 获取选中的行
     * @returns {JobApplication[]} 选中的投递记录数组
     */
    getSelectedRows() {
        return this.applications.filter(app => this.selectedRows.has(app.id));
    }

    /**
     * 添加新行
     * @param {Partial<JobApplication>} data 初始数据
     */
    async addRow(data = {}) {
        try {
            const newApplication = this.dataService.createApplication(data);
            await this.dataService.saveApplication(newApplication);
            
            this.applications.unshift(newApplication);
            this.render(this.applications);
            
            this.dispatchEvent('dataChanged', { type: 'add', application: newApplication });
            showNotification('添加成功', 'success');
            
        } catch (error) {
            console.error('添加记录失败:', error);
            showNotification('添加失败: ' + error.message, 'error');
        }
    }

    /**
     * 删除选中的行
     */
    async deleteSelectedRows() {
        if (this.selectedRows.size === 0) {
            showNotification('请先选择要删除的记录', 'warning');
            return;
        }

        if (!confirm(`确定要删除选中的 ${this.selectedRows.size} 条记录吗？`)) {
            return;
        }

        try {
            const selectedIds = Array.from(this.selectedRows);
            await this.dataService.deleteApplications(selectedIds);
            
            this.applications = this.applications.filter(app => !selectedIds.includes(app.id));
            this.selectedRows.clear();
            this.render(this.applications);
            
            this.dispatchEvent('dataChanged', { type: 'delete', ids: selectedIds });
            showNotification('删除成功', 'success');
            
        } catch (error) {
            console.error('删除记录失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        }
    }

    /**
     * 将选中行移到顶部
     */
    moveSelectedToTop() {
        if (this.selectedRows.size === 0) {
            showNotification('请先选择要置顶的记录', 'warning');
            return;
        }

        const selectedApps = this.getSelectedRows();
        const otherApps = this.applications.filter(app => !this.selectedRows.has(app.id));
        
        this.applications = [...selectedApps, ...otherApps];
        this.render(this.applications);
        
        this.dispatchEvent('dataChanged', { type: 'reorder' });
        showNotification('置顶成功', 'success');
    }

    /**
     * 在选中位置插入新行
     */
    async insertRowAtSelection() {
        const selectedIds = Array.from(this.selectedRows);
        if (selectedIds.length === 0) {
            // 如果没有选中行，在顶部插入
            await this.addRow();
            return;
        }

        // 在第一个选中行的位置插入
        const firstSelectedId = selectedIds[0];
        const insertIndex = this.applications.findIndex(app => app.id === firstSelectedId);
        
        try {
            const newApplication = this.dataService.createApplication();
            await this.dataService.saveApplication(newApplication);
            
            this.applications.splice(insertIndex, 0, newApplication);
            this.render(this.applications);
            
            this.dispatchEvent('dataChanged', { type: 'insert', application: newApplication });
            showNotification('插入成功', 'success');
            
        } catch (error) {
            console.error('插入记录失败:', error);
            showNotification('插入失败: ' + error.message, 'error');
        }
    }

    /**
     * 编辑应用详情
     * @param {string} applicationId 应用ID
     */
    editApplication(applicationId) {
        // 这里可以打开详细编辑对话框
        // 暂时使用简单的提示
        showNotification('详细编辑功能开发中...', 'info');
    }

    /**
     * 删除单个应用
     * @param {string} applicationId 应用ID
     */
    async deleteApplication(applicationId) {
        if (!confirm('确定要删除这条记录吗？')) {
            return;
        }

        try {
            await this.dataService.deleteApplication(applicationId);
            
            this.applications = this.applications.filter(app => app.id !== applicationId);
            this.selectedRows.delete(applicationId);
            this.render(this.applications);
            
            this.dispatchEvent('dataChanged', { type: 'delete', ids: [applicationId] });
            showNotification('删除成功', 'success');
            
        } catch (error) {
            console.error('删除记录失败:', error);
            showNotification('删除失败: ' + error.message, 'error');
        }
    }

    /**
     * 处理键盘事件
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyDown(e) {
        // 如果正在编辑，不处理快捷键
        if (this.editingCell) return;

        switch (e.key) {
            case 'Delete':
                if (this.selectedRows.size > 0) {
                    this.deleteSelectedRows();
                }
                break;
            case 'Insert':
                this.insertRowAtSelection();
                break;
            case 'Escape':
                this.selectedRows.clear();
                this.updateRowSelection();
                this.updateSelectAllCheckbox();
                break;
        }
    }

    /**
     * 分发自定义事件
     * @param {string} eventName 事件名称
     * @param {any} detail 事件详情
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        this.container.dispatchEvent(event);
    }

    /**
     * 刷新表格数据
     */
    async refresh() {
        try {
            const applications = await this.dataService.getAllApplications();
            this.render(applications);
        } catch (error) {
            console.error('刷新数据失败:', error);
            showNotification('刷新失败: ' + error.message, 'error');
        }
    }

    /**
     * 清空选择
     */
    clearSelection() {
        this.selectedRows.clear();
        this.updateRowSelection();
        this.updateSelectAllCheckbox();
        this.dispatchEvent('selectionChanged', { selectedRows: [] });
    }

    /**
     * 获取表格数据
     * @returns {JobApplication[]} 当前表格数据
     */
    getData() {
        return [...this.applications];
    }

    /**
     * 设置表格数据
     * @param {JobApplication[]} applications 投递记录数组
     */
    setData(applications) {
        this.render(applications);
    }

    /**
     * 获取选中行数量
     * @returns {number} 选中行数量
     */
    getSelectedCount() {
        return this.selectedRows.size;
    }

    /**
     * 是否有选中行
     * @returns {boolean} 是否有选中行
     */
    hasSelection() {
        return this.selectedRows.size > 0;
    }

    /**
     * 销毁表格管理器
     */
    destroy() {
        // 清理事件监听器
        this.selectAllCheckbox.removeEventListener('change', this.toggleSelectAll);

        // 清理数据
        this.applications = [];
        this.selectedRows.clear();
        this.editingCell = null;

        // 清空表格
        this.tbody.innerHTML = '';
    }
}

// 导出类
window.TableManager = TableManager;
