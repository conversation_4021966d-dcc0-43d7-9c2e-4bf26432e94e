# 需求文档

## 介绍

本功能是一个Edge浏览器插件，用于帮助求职者管理和跟踪简历投递进展。用户在投递简历后，可以通过点击插件来记录投递进度，并通过一个功能完整的表格来管理所有投递记录。该插件提供了完整的数据管理功能，包括撤销重做、行操作、数据导出等。

## 需求

### 需求 1

**用户故事：** 作为求职者，我希望能够通过浏览器插件快速记录简历投递信息，以便系统化地管理我的求职进展。

#### 验收标准

1. 当用户在任何网页上点击Edge插件图标时，系统应显示投递记录表界面
2. 当用户点击"添加新记录"按钮时，系统应提供表单来输入投递信息
3. 当用户填写完投递信息并保存时，系统应将记录添加到表格中
4. 系统应支持以下字段的录入：公司、投递链接、重视度、行业、标签、职位、地点、进展、状态进度、进展时间、投递时间、备注、内推码

### 需求 2

**用户故事：** 作为求职者，我希望能够通过不同的重视度级别和颜色来区分投递的重要性，以便优先关注重要的机会。

#### 验收标准

1. 当用户设置重视度时，系统应提供1级到3级的选择
2. 当重视度为1级时，系统应显示对应的颜色标识
3. 当重视度为2级时，系统应显示不同的颜色标识
4. 当重视度为3级时，系统应显示第三种颜色标识
5. 系统应在表格中通过颜色直观地显示每条记录的重视度

### 需求 3

**用户故事：** 作为求职者，我希望能够通过下拉菜单选择投递进展和状态，以便准确记录每个申请的当前状态。

#### 验收标准

1. 当用户编辑进展字段时，系统应提供下拉菜单包含：已投递、测评、笔试、AI面、一面、二面、三面、HR终面、谈offer、签约
2. 当用户编辑状态进度字段时，系统应提供下拉菜单包含：等消息、等我回复、等待开始、已过、未过、已放弃、被调剂、解约
3. 当用户选择任一选项时，系统应立即更新记录
4. 系统应保存用户的选择并在重新打开时保持状态

### 需求 4

**用户故事：** 作为求职者，我希望能够对表格进行各种操作，以便灵活管理我的投递记录。

#### 验收标准

1. 当用户执行任何操作时，系统应支持撤销功能
2. 当用户撤销操作后，系统应支持重做功能
3. 当用户选择表格行并点击"插入新行"时，系统应在选中位置插入空白行
4. 当用户选择表格行并点击"置顶"时，系统应将选中行移动到表格顶部
5. 当用户选择表格行并点击"删除"时，系统应删除选中的行
6. 系统应在执行删除操作前提供确认提示

### 需求 5

**用户故事：** 作为求职者，我希望能够导出我的投递记录表格，以便进行备份或在其他地方使用这些数据。

#### 验收标准

1. 当用户点击"导出表格"按钮时，系统应提供导出格式选择
2. 当用户选择Excel格式时，系统应生成包含所有字段的Excel文件
3. 当用户选择CSV格式时，系统应生成CSV文件
4. 当导出完成时，系统应自动下载文件到用户的默认下载目录
5. 导出的文件应包含所有表格字段和数据

### 需求 6

**用户故事：** 作为求职者，我希望插件能够持久化保存我的数据，以便在关闭浏览器后重新打开时数据仍然存在。

#### 验收标准

1. 当用户添加或修改记录时，系统应自动保存数据到浏览器本地存储
2. 当用户重新打开插件时，系统应加载之前保存的所有记录
3. 当用户在不同设备上使用同一Edge账户时，系统应支持数据同步（如果可能）
4. 系统应在数据保存失败时提供错误提示

### 需求 7

**用户故事：** 作为求职者，我希望插件界面简洁易用，以便快速完成投递记录的管理。

#### 验收标准

1. 当插件打开时，系统应在合理的时间内（少于2秒）显示界面
2. 当用户操作表格时，系统应提供流畅的用户体验
3. 系统应使用清晰的图标和按钮标识各种功能
4. 当用户悬停在功能按钮上时，系统应显示工具提示说明
5. 系统应支持键盘快捷键进行常用操作