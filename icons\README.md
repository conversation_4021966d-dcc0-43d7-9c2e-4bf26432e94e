# 插件图标说明

## 图标文件

本插件需要以下尺寸的图标文件：
- icon16.png (16x16像素)
- icon32.png (32x32像素)  
- icon48.png (48x48像素)
- icon128.png (128x128像素)

## 生成图标

已提供了 `icon.svg` 源文件，您可以使用以下方法生成所需的PNG图标：

### 方法1：使用在线工具
1. 访问 https://convertio.co/svg-png/ 或类似的在线转换工具
2. 上传 `icon.svg` 文件
3. 设置输出尺寸为 16x16, 32x32, 48x48, 128x128
4. 下载生成的PNG文件并重命名

### 方法2：使用Inkscape (免费软件)
```bash
# 安装Inkscape后，使用命令行生成
inkscape icon.svg --export-png=icon16.png --export-width=16 --export-height=16
inkscape icon.svg --export-png=icon32.png --export-width=32 --export-height=32
inkscape icon.svg --export-png=icon48.png --export-width=48 --export-height=48
inkscape icon.svg --export-png=icon128.png --export-width=128 --export-height=128
```

### 方法3：使用ImageMagick
```bash
# 安装ImageMagick后，使用命令行生成
convert icon.svg -resize 16x16 icon16.png
convert icon.svg -resize 32x32 icon32.png
convert icon.svg -resize 48x48 icon48.png
convert icon.svg -resize 128x128 icon128.png
```

### 方法4：使用Adobe Illustrator或其他矢量图形软件
1. 打开 `icon.svg` 文件
2. 导出为PNG格式
3. 设置相应的尺寸

## 图标设计说明

图标设计包含以下元素：
- 蓝色圆形背景 (#007acc) - 代表专业性
- 白色文档图标 - 代表简历/投递记录
- 绿色勾选标记 - 代表进展跟踪
- "求职"文字 - 明确插件用途

## 临时解决方案

如果暂时无法生成PNG图标，可以：
1. 使用浏览器打开 `icon.svg`
2. 截图并调整为所需尺寸
3. 或者使用任何简单的图标作为占位符

## 注意事项

- 确保图标文件名与 `manifest.json` 中的配置一致
- PNG图标应该有透明背景以获得最佳效果
- 建议使用高质量的图标以确保在不同设备上的显示效果
