# 求职投递进展记录插件

一个专为求职者设计的Edge浏览器插件，帮助您系统化地管理和跟踪简历投递进展。

## 功能特性

### 📝 投递记录管理
- 添加、编辑、删除投递记录
- 支持公司、职位、投递链接、重视度等多个字段
- 内联编辑，快速修改记录信息

### 🎯 进展跟踪
- 多阶段进展状态：已投递、测评、笔试、AI面、一面、二面、三面、HR终面、谈offer、签约
- 状态进度：等消息、等我回复、等待开始、已过、未过、已放弃、被调剂、解约
- 重视度分级（1-3级）和颜色标识

### 🔄 操作历史
- 撤销/重做功能，支持50步操作历史
- 智能操作记录，防止误操作

### 📊 数据导出
- 支持Excel和CSV格式导出
- 包含所有字段的完整数据

### 🎨 用户体验
- 响应式设计，适配不同屏幕尺寸
- 全屏页面显示，更好的操作体验
- 实时统计信息显示
- 键盘快捷键支持

## 安装步骤

### 1. 准备图标文件
由于技术限制，需要手动生成图标文件：

1. 打开 `icons/generate_icons.html` 文件
2. 点击"生成图标"按钮
3. 依次下载并保存为：
   - `icon16.png` (16x16像素)
   - `icon32.png` (32x32像素)
   - `icon48.png` (48x48像素)
   - `icon128.png` (128x128像素)
4. 将这些文件放入 `icons/` 目录

### 2. 安装插件
1. 打开Microsoft Edge浏览器
2. 访问 `edge://extensions/`
3. 开启右上角的"开发人员模式"
4. 点击"加载解压缩的扩展"
5. 选择插件根目录（包含manifest.json的文件夹）
6. 插件安装完成

### 3. 使用插件
- 点击浏览器工具栏中的插件图标
- 插件将在新标签页中打开，提供完整的管理界面

## 使用指南

### 基本操作
- **添加记录**：点击"添加"按钮创建新的投递记录
- **编辑记录**：双击表格单元格进行内联编辑
- **选择记录**：使用复选框选择单个或多个记录
- **排序**：点击列标题进行排序

### 批量操作
- **插入**：在选中位置插入新记录
- **置顶**：将选中记录移动到表格顶部
- **删除**：删除选中的记录（支持批量删除）

### 快捷键
- `Ctrl + N`：添加新记录
- `Ctrl + Z`：撤销操作
- `Ctrl + Y` 或 `Ctrl + Shift + Z`：重做操作
- `Ctrl + E`：打开导出对话框
- `Delete`：删除选中记录
- `Escape`：取消选择

### 数据导出
1. 点击"导出"按钮
2. 选择导出格式（Excel或CSV）
3. 文件将自动下载到默认下载目录

## 文件结构

```
求职投递进展记录插件/
├── manifest.json           # 插件配置文件
├── popup.html             # 主界面HTML
├── popup.js               # 主控制器
├── background.js          # 后台脚本
├── styles/
│   ├── main.css          # 主样式文件
│   └── table.css         # 表格样式
├── scripts/
│   ├── utils.js          # 工具函数
│   ├── dataService.js    # 数据服务
│   ├── tableManager.js   # 表格管理
│   ├── historyManager.js # 历史管理
│   └── exportManager.js  # 导出功能
├── icons/
│   ├── icon.svg          # 源图标文件
│   ├── generate_icons.html # 图标生成工具
│   └── README.md         # 图标说明
├── _locales/
│   └── zh_CN/
│       └── messages.json # 中文本地化
└── README.md             # 说明文档
```

## 技术特性

### 架构设计
- **模块化设计**：各功能模块独立，便于维护和扩展
- **事件驱动**：组件间通过事件通信，降低耦合度
- **数据持久化**：使用Chrome Storage API本地存储数据

### 兼容性
- Microsoft Edge 88+
- 支持Manifest V3规范
- 响应式设计，支持各种屏幕尺寸

### 性能优化
- 防抖处理，减少不必要的操作
- 虚拟滚动（计划中），处理大量数据
- 增量更新，只更新变化的部分

## 常见问题

### Q: 插件图标不显示怎么办？
A: 请确保已按照安装步骤生成并放置了PNG格式的图标文件。

### Q: 数据会丢失吗？
A: 数据存储在浏览器本地，除非手动清除浏览器数据，否则不会丢失。

### Q: 可以在多台设备间同步数据吗？
A: 目前使用本地存储，不支持跨设备同步。未来版本可能会添加云同步功能。

### Q: 导出的CSV文件中文显示乱码？
A: 插件已添加BOM头解决中文乱码问题。如仍有问题，请使用Excel打开CSV文件。

### Q: 支持导入数据吗？
A: 当前版本不支持数据导入，这是未来版本的计划功能。

## 开发说明

### 本地开发
1. 克隆或下载项目文件
2. 按照安装步骤加载插件
3. 修改代码后，在扩展管理页面点击"重新加载"

### 贡献代码
欢迎提交Issue和Pull Request来改进插件功能。

## 版本历史

### v1.0.0
- 基础功能实现
- 投递记录管理
- 进展跟踪
- 数据导出
- 撤销重做
- 响应式设计

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者邮箱

---

感谢使用求职投递进展记录插件！祝您求职顺利！🎉
