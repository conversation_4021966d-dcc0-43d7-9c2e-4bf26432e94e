// 数据服务类 - 处理所有数据操作

/**
 * 进展状态枚举
 */
const ProgressStatus = {
    APPLIED: '已投递',
    ASSESSMENT: '测评',
    WRITTEN_TEST: '笔试',
    AI_INTERVIEW: 'AI面',
    FIRST_INTERVIEW: '一面',
    SECOND_INTERVIEW: '二面',
    THIRD_INTERVIEW: '三面',
    HR_FINAL: 'HR终面',
    OFFER_NEGOTIATION: '谈offer',
    SIGNED: '签约'
};

/**
 * 状态进度枚举
 */
const StatusProgress = {
    WAITING_RESPONSE: '等消息',
    WAITING_MY_REPLY: '等我回复',
    WAITING_START: '等待开始',
    PASSED: '已过',
    FAILED: '未过',
    ABANDONED: '已放弃',
    TRANSFERRED: '被调剂',
    TERMINATED: '解约'
};

/**
 * 数据服务类
 */
class DataService {
    constructor() {
        this.storageKey = 'jobApplications';
        this.settingsKey = 'settings';
        this.metadataKey = 'metadata';
    }

    /**
     * 获取所有投递记录
     * @returns {Promise<JobApplication[]>} 投递记录数组
     */
    async getAllApplications() {
        try {
            const result = await chrome.storage.local.get([this.storageKey]);
            const applications = result[this.storageKey] || [];
            
            // 确保数据格式正确
            return applications.map(app => this.validateAndFixApplication(app));
        } catch (error) {
            console.error('获取投递记录失败:', error);
            throw new Error('获取数据失败');
        }
    }

    /**
     * 保存投递记录
     * @param {JobApplication} application 投递记录
     * @returns {Promise<void>}
     */
    async saveApplication(application) {
        try {
            // 验证数据
            const validatedApp = this.validateAndFixApplication(application);
            
            // 获取现有数据
            const applications = await this.getAllApplications();
            
            // 检查是否已存在
            const existingIndex = applications.findIndex(app => app.id === validatedApp.id);
            
            if (existingIndex >= 0) {
                // 更新现有记录
                applications[existingIndex] = validatedApp;
            } else {
                // 添加新记录
                applications.push(validatedApp);
            }
            
            // 保存到存储
            await chrome.storage.local.set({
                [this.storageKey]: applications
            });
            
            // 更新元数据
            await this.updateMetadata(applications.length);
            
        } catch (error) {
            console.error('保存投递记录失败:', error);
            throw new Error('保存数据失败');
        }
    }

    /**
     * 更新投递记录
     * @param {string} id 记录ID
     * @param {Partial<JobApplication>} updates 更新数据
     * @returns {Promise<void>}
     */
    async updateApplication(id, updates) {
        try {
            const applications = await this.getAllApplications();
            const index = applications.findIndex(app => app.id === id);
            
            if (index === -1) {
                throw new Error('记录不存在');
            }
            
            // 合并更新数据
            const updatedApp = {
                ...applications[index],
                ...updates,
                updatedAt: new Date()
            };
            
            // 验证更新后的数据
            applications[index] = this.validateAndFixApplication(updatedApp);
            
            // 保存到存储
            await chrome.storage.local.set({
                [this.storageKey]: applications
            });
            
        } catch (error) {
            console.error('更新投递记录失败:', error);
            throw new Error('更新数据失败');
        }
    }

    /**
     * 删除投递记录
     * @param {string} id 记录ID
     * @returns {Promise<void>}
     */
    async deleteApplication(id) {
        try {
            const applications = await this.getAllApplications();
            const filteredApps = applications.filter(app => app.id !== id);
            
            if (filteredApps.length === applications.length) {
                throw new Error('记录不存在');
            }
            
            // 保存到存储
            await chrome.storage.local.set({
                [this.storageKey]: filteredApps
            });
            
            // 更新元数据
            await this.updateMetadata(filteredApps.length);
            
        } catch (error) {
            console.error('删除投递记录失败:', error);
            throw new Error('删除数据失败');
        }
    }

    /**
     * 批量删除投递记录
     * @param {string[]} ids 记录ID数组
     * @returns {Promise<void>}
     */
    async deleteApplications(ids) {
        try {
            const applications = await this.getAllApplications();
            const filteredApps = applications.filter(app => !ids.includes(app.id));
            
            // 保存到存储
            await chrome.storage.local.set({
                [this.storageKey]: filteredApps
            });
            
            // 更新元数据
            await this.updateMetadata(filteredApps.length);
            
        } catch (error) {
            console.error('批量删除投递记录失败:', error);
            throw new Error('删除数据失败');
        }
    }

    /**
     * 创建新的投递记录
     * @param {Partial<JobApplication>} data 投递记录数据
     * @returns {JobApplication} 完整的投递记录
     */
    createApplication(data = {}) {
        const now = new Date();
        
        return this.validateAndFixApplication({
            id: generateId(),
            company: data.company || '',
            applicationLink: data.applicationLink || '',
            priority: data.priority || 2,
            industry: data.industry || '',
            tags: data.tags || [],
            position: data.position || '',
            location: data.location || '',
            progress: data.progress || ProgressStatus.APPLIED,
            statusProgress: data.statusProgress || StatusProgress.WAITING_RESPONSE,
            progressTime: data.progressTime || now,
            applicationTime: data.applicationTime || now,
            notes: data.notes || '',
            referralCode: data.referralCode || '',
            createdAt: data.createdAt || now,
            updatedAt: data.updatedAt || now
        });
    }

    /**
     * 验证并修复投递记录数据
     * @param {any} application 投递记录数据
     * @returns {JobApplication} 验证后的投递记录
     */
    validateAndFixApplication(application) {
        const now = new Date();
        
        return {
            id: application.id || generateId(),
            company: String(application.company || ''),
            applicationLink: String(application.applicationLink || ''),
            priority: Math.max(1, Math.min(3, parseInt(application.priority) || 2)),
            industry: String(application.industry || ''),
            tags: Array.isArray(application.tags) ? application.tags : [],
            position: String(application.position || ''),
            location: String(application.location || ''),
            progress: Object.values(ProgressStatus).includes(application.progress) 
                ? application.progress 
                : ProgressStatus.APPLIED,
            statusProgress: Object.values(StatusProgress).includes(application.statusProgress)
                ? application.statusProgress
                : StatusProgress.WAITING_RESPONSE,
            progressTime: parseDate(application.progressTime) || now,
            applicationTime: parseDate(application.applicationTime) || now,
            notes: String(application.notes || ''),
            referralCode: String(application.referralCode || ''),
            createdAt: parseDate(application.createdAt) || now,
            updatedAt: parseDate(application.updatedAt) || now
        };
    }

    /**
     * 更新元数据
     * @param {number} totalRecords 总记录数
     * @returns {Promise<void>}
     */
    async updateMetadata(totalRecords) {
        try {
            const metadata = {
                version: '1.0.0',
                lastBackup: new Date(),
                totalRecords: totalRecords
            };
            
            await chrome.storage.local.set({
                [this.metadataKey]: metadata
            });
        } catch (error) {
            console.error('更新元数据失败:', error);
        }
    }

    /**
     * 获取设置
     * @returns {Promise<Object>} 设置对象
     */
    async getSettings() {
        try {
            const result = await chrome.storage.sync.get([this.settingsKey]);
            return result[this.settingsKey] || {
                priorityColors: { 1: '#ff4444', 2: '#ffaa00', 3: '#44ff44' },
                defaultView: 'table',
                autoSave: true
            };
        } catch (error) {
            console.error('获取设置失败:', error);
            return {};
        }
    }

    /**
     * 保存设置
     * @param {Object} settings 设置对象
     * @returns {Promise<void>}
     */
    async saveSettings(settings) {
        try {
            await chrome.storage.sync.set({
                [this.settingsKey]: settings
            });
        } catch (error) {
            console.error('保存设置失败:', error);
            throw new Error('保存设置失败');
        }
    }
}

// 导出常量和类
window.ProgressStatus = ProgressStatus;
window.StatusProgress = StatusProgress;
window.DataService = DataService;
