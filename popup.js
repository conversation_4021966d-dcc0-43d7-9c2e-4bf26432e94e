// 主控制器 - 整合所有组件并实现用户交互逻辑

class JobApplicationTracker {
    constructor() {
        this.dataService = null;
        this.tableManager = null;
        this.historyManager = null;
        this.exportManager = null;
        
        this.isInitialized = false;
        this.isLoading = false;
        
        this.initializeComponents();
        this.bindEvents();
    }

    /**
     * 初始化组件
     */
    async initializeComponents() {
        try {
            this.showLoading(true);
            
            // 初始化数据服务
            this.dataService = new DataService();
            
            // 初始化历史管理器
            this.historyManager = new HistoryManager(50);
            this.historyManager.setActionExecutor(this.executeHistoryAction.bind(this));
            
            // 初始化导出管理器
            this.exportManager = new ExportManager();
            
            // 初始化表格管理器
            const container = document.querySelector('.container');
            this.tableManager = new TableManager(container, this.dataService);
            
            // 加载数据
            await this.loadData();
            
            // 更新UI状态
            this.updateUIState();
            
            this.isInitialized = true;
            this.showLoading(false);
            
            console.log('求职投递进展记录插件初始化完成');
            
        } catch (error) {
            console.error('初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
            this.showLoading(false);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 工具栏按钮事件
        this.bindToolbarEvents();
        
        // 底部操作栏事件
        this.bindBottomToolbarEvents();
        
        // 导出模态框事件
        this.bindExportModalEvents();
        
        // 表格事件
        this.bindTableEvents();
        
        // 历史管理事件
        this.bindHistoryEvents();
        
        // 键盘快捷键
        this.bindKeyboardShortcuts();
        
        // 窗口事件
        this.bindWindowEvents();
    }

    /**
     * 绑定工具栏事件
     */
    bindToolbarEvents() {
        // 添加按钮
        const addBtn = document.getElementById('addBtn');
        addBtn?.addEventListener('click', () => this.addNewRecord());
        
        // 导出按钮
        const exportBtn = document.getElementById('exportBtn');
        exportBtn?.addEventListener('click', () => this.showExportModal());
        
        // 撤销按钮
        const undoBtn = document.getElementById('undoBtn');
        undoBtn?.addEventListener('click', () => this.undo());
        
        // 重做按钮
        const redoBtn = document.getElementById('redoBtn');
        redoBtn?.addEventListener('click', () => this.redo());
    }

    /**
     * 绑定底部工具栏事件
     */
    bindBottomToolbarEvents() {
        // 插入按钮
        const insertBtn = document.getElementById('insertBtn');
        insertBtn?.addEventListener('click', () => this.insertRecord());
        
        // 置顶按钮
        const moveTopBtn = document.getElementById('moveTopBtn');
        moveTopBtn?.addEventListener('click', () => this.moveToTop());
        
        // 删除按钮
        const deleteBtn = document.getElementById('deleteBtn');
        deleteBtn?.addEventListener('click', () => this.deleteSelected());
    }

    /**
     * 绑定导出模态框事件
     */
    bindExportModalEvents() {
        const exportModal = document.getElementById('exportModal');
        const modalClose = exportModal?.querySelector('.modal-close');
        const exportExcel = document.getElementById('exportExcel');
        const exportCSV = document.getElementById('exportCSV');
        
        // 关闭模态框
        modalClose?.addEventListener('click', () => this.hideExportModal());
        exportModal?.addEventListener('click', (e) => {
            if (e.target === exportModal) {
                this.hideExportModal();
            }
        });
        
        // 导出Excel
        exportExcel?.addEventListener('click', () => {
            this.exportData('excel');
            this.hideExportModal();
        });
        
        // 导出CSV
        exportCSV?.addEventListener('click', () => {
            this.exportData('csv');
            this.hideExportModal();
        });
    }

    /**
     * 绑定表格事件
     */
    bindTableEvents() {
        const container = document.querySelector('.container');
        
        // 监听表格数据变更
        container?.addEventListener('dataChanged', (e) => {
            this.handleDataChanged(e.detail);
        });
        
        // 监听选择变更
        container?.addEventListener('selectionChanged', (e) => {
            this.handleSelectionChanged(e.detail);
        });
    }

    /**
     * 绑定历史管理事件
     */
    bindHistoryEvents() {
        // 监听历史状态变更
        window.addEventListener('historyChanged', (e) => {
            this.updateHistoryButtons(e.detail);
        });
    }

    /**
     * 绑定键盘快捷键
     */
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Z 撤销
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.undo();
            }
            // Ctrl+Y 或 Ctrl+Shift+Z 重做
            else if (e.ctrlKey && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
                e.preventDefault();
                this.redo();
            }
            // Ctrl+N 添加新记录
            else if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.addNewRecord();
            }
            // Ctrl+E 导出
            else if (e.ctrlKey && e.key === 'e') {
                e.preventDefault();
                this.showExportModal();
            }
            // Delete 删除选中
            else if (e.key === 'Delete' && !e.ctrlKey && !e.altKey) {
                this.deleteSelected();
            }
        });
    }

    /**
     * 绑定窗口事件
     */
    bindWindowEvents() {
        // 窗口关闭前保存数据
        window.addEventListener('beforeunload', () => {
            this.saveCurrentState();
        });
        
        // 窗口失去焦点时保存数据
        window.addEventListener('blur', () => {
            this.saveCurrentState();
        });
    }

    /**
     * 加载数据
     */
    async loadData() {
        try {
            const applications = await this.dataService.getAllApplications();
            this.tableManager.render(applications);

            // 更新统计信息
            this.updateStats();

            console.log(`加载了 ${applications.length} 条投递记录`);

        } catch (error) {
            console.error('加载数据失败:', error);
            this.showError('加载数据失败: ' + error.message);
        }
    }

    /**
     * 添加新记录
     */
    async addNewRecord() {
        try {
            await this.tableManager.addRow();
            
            // 记录历史操作
            const applications = this.tableManager.getData();
            if (applications.length > 0) {
                const newApp = applications[0]; // 新记录在顶部
                this.historyManager.recordAction(
                    ActionType.ADD,
                    HistoryManager.createActionData.add(newApp, 0)
                );
            }
            
        } catch (error) {
            console.error('添加记录失败:', error);
            this.showError('添加记录失败: ' + error.message);
        }
    }

    /**
     * 插入记录
     */
    async insertRecord() {
        try {
            await this.tableManager.insertRowAtSelection();
            
        } catch (error) {
            console.error('插入记录失败:', error);
            this.showError('插入记录失败: ' + error.message);
        }
    }

    /**
     * 移动到顶部
     */
    moveToTop() {
        try {
            const selectedRows = this.tableManager.getSelectedRows();
            if (selectedRows.length === 0) {
                this.showWarning('请先选择要置顶的记录');
                return;
            }

            // 记录历史操作
            const oldOrder = this.tableManager.getData().map(app => app.id);
            
            this.tableManager.moveSelectedToTop();
            
            const newOrder = this.tableManager.getData().map(app => app.id);
            this.historyManager.recordAction(
                ActionType.REORDER,
                HistoryManager.createActionData.reorder(oldOrder, newOrder)
            );
            
        } catch (error) {
            console.error('置顶失败:', error);
            this.showError('置顶失败: ' + error.message);
        }
    }

    /**
     * 删除选中记录
     */
    async deleteSelected() {
        try {
            const selectedRows = this.tableManager.getSelectedRows();
            if (selectedRows.length === 0) {
                this.showWarning('请先选择要删除的记录');
                return;
            }

            // 记录历史操作
            const applications = this.tableManager.getData();
            const indices = selectedRows.map(app => 
                applications.findIndex(a => a.id === app.id)
            );
            
            this.historyManager.recordAction(
                ActionType.BATCH_DELETE,
                HistoryManager.createActionData.batchDelete(selectedRows, indices)
            );

            await this.tableManager.deleteSelectedRows();
            
        } catch (error) {
            console.error('删除失败:', error);
            this.showError('删除失败: ' + error.message);
        }
    }

    /**
     * 撤销操作
     */
    async undo() {
        try {
            if (!this.historyManager.canUndo()) {
                this.showInfo('没有可撤销的操作');
                return;
            }

            const action = await this.historyManager.undo();
            if (action) {
                this.showSuccess(`已撤销: ${action.getDescription()}`);
                await this.refreshTable();
            }
            
        } catch (error) {
            console.error('撤销失败:', error);
            this.showError('撤销失败: ' + error.message);
        }
    }

    /**
     * 重做操作
     */
    async redo() {
        try {
            if (!this.historyManager.canRedo()) {
                this.showInfo('没有可重做的操作');
                return;
            }

            const action = await this.historyManager.redo();
            if (action) {
                this.showSuccess(`已重做: ${action.getDescription()}`);
                await this.refreshTable();
            }
            
        } catch (error) {
            console.error('重做失败:', error);
            this.showError('重做失败: ' + error.message);
        }
    }

    /**
     * 显示导出模态框
     */
    showExportModal() {
        const applications = this.tableManager.getData();
        if (applications.length === 0) {
            this.showWarning('没有数据可导出');
            return;
        }

        const modal = document.getElementById('exportModal');
        if (modal) {
            modal.style.display = 'flex';
        }
    }

    /**
     * 隐藏导出模态框
     */
    hideExportModal() {
        const modal = document.getElementById('exportModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }

    /**
     * 导出数据
     * @param {string} format 导出格式
     */
    async exportData(format) {
        try {
            const applications = this.tableManager.getData();
            if (applications.length === 0) {
                this.showWarning('没有数据可导出');
                return;
            }

            this.showLoading(true, '正在导出数据...');
            
            await this.exportManager.exportData(applications, format);
            
            this.showSuccess(`导出${format.toUpperCase()}成功`);
            
        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    /**
     * 处理数据变更事件
     * @param {Object} detail 事件详情
     */
    handleDataChanged(detail) {
        // 更新UI状态
        this.updateUIState();

        // 更新统计信息
        this.updateStats();

        // 自动保存
        this.autoSave();
    }

    /**
     * 处理选择变更事件
     * @param {Object} detail 事件详情
     */
    handleSelectionChanged(detail) {
        this.updateSelectionButtons(detail.selectedRows);
        this.updateStats();
    }

    /**
     * 执行历史操作
     * @param {Action} action 要执行的操作
     */
    async executeHistoryAction(action) {
        switch (action.type) {
            case ActionType.ADD:
                await this.dataService.saveApplication(action.data.application);
                break;
                
            case ActionType.DELETE:
                await this.dataService.deleteApplication(action.data.id);
                break;
                
            case ActionType.UPDATE:
                await this.dataService.updateApplication(action.data.id, action.data.newValues);
                break;
                
            case ActionType.BATCH_DELETE:
                const ids = action.data.applications.map(app => app.id);
                await this.dataService.deleteApplications(ids);
                break;
                
            case ActionType.REORDER:
                // 重新排序操作需要重新加载数据
                break;
                
            default:
                console.warn('未知的历史操作类型:', action.type);
        }
    }

    /**
     * 刷新表格
     */
    async refreshTable() {
        await this.loadData();
    }

    /**
     * 更新UI状态
     */
    updateUIState() {
        const applications = this.tableManager?.getData() || [];
        const hasData = applications.length > 0;
        
        // 更新导出按钮状态
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.disabled = !hasData;
        }
    }

    /**
     * 更新选择相关按钮状态
     * @param {string[]} selectedRows 选中的行ID数组
     */
    updateSelectionButtons(selectedRows) {
        const hasSelection = selectedRows.length > 0;
        
        // 更新底部工具栏按钮状态
        const insertBtn = document.getElementById('insertBtn');
        const moveTopBtn = document.getElementById('moveTopBtn');
        const deleteBtn = document.getElementById('deleteBtn');
        
        if (insertBtn) insertBtn.disabled = false; // 插入按钮始终可用
        if (moveTopBtn) moveTopBtn.disabled = !hasSelection;
        if (deleteBtn) deleteBtn.disabled = !hasSelection;
    }

    /**
     * 更新历史按钮状态
     * @param {Object} historyState 历史状态
     */
    updateHistoryButtons(historyState) {
        const undoBtn = document.getElementById('undoBtn');
        const redoBtn = document.getElementById('redoBtn');

        if (undoBtn) {
            undoBtn.disabled = !historyState.canUndo;
            undoBtn.title = historyState.canUndo
                ? `撤销: ${historyState.lastAction?.getDescription() || ''}`
                : '撤销';
        }

        if (redoBtn) {
            redoBtn.disabled = !historyState.canRedo;
            redoBtn.title = historyState.canRedo
                ? `重做: ${historyState.nextRedoAction?.getDescription() || ''}`
                : '重做';
        }
    }

    /**
     * 更新统计信息
     */
    updateStats() {
        const applications = this.tableManager?.getData() || [];
        const selectedCount = this.tableManager?.getSelectedCount() || 0;

        // 更新总记录数
        const totalCountEl = document.getElementById('totalCount');
        if (totalCountEl) {
            totalCountEl.textContent = applications.length;
        }

        // 更新选中数量
        const selectedCountEl = document.getElementById('selectedCount');
        if (selectedCountEl) {
            selectedCountEl.textContent = selectedCount;
        }

        // 更新最后更新时间
        const lastUpdateEl = document.getElementById('lastUpdate');
        if (lastUpdateEl) {
            const now = new Date();
            lastUpdateEl.textContent = formatDateTime(now);
        }
    }

    /**
     * 自动保存
     */
    autoSave() {
        // 防抖保存
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setTimeout(() => {
            this.saveCurrentState();
        }, 1000);
    }

    /**
     * 保存当前状态
     */
    async saveCurrentState() {
        try {
            // 这里可以保存一些用户偏好设置
            const settings = await this.dataService.getSettings();
            await this.dataService.saveSettings(settings);
            
        } catch (error) {
            console.error('保存状态失败:', error);
        }
    }

    /**
     * 显示加载状态
     * @param {boolean} show 是否显示
     * @param {string} message 加载消息
     */
    showLoading(show, message = '加载中...') {
        this.isLoading = show;
        
        // 这里可以显示加载指示器
        if (show) {
            console.log(message);
        }
    }

    /**
     * 显示成功消息
     * @param {string} message 消息内容
     */
    showSuccess(message) {
        showNotification(message, 'success');
    }

    /**
     * 显示错误消息
     * @param {string} message 消息内容
     */
    showError(message) {
        showNotification(message, 'error');
    }

    /**
     * 显示警告消息
     * @param {string} message 消息内容
     */
    showWarning(message) {
        showNotification(message, 'warning');
    }

    /**
     * 显示信息消息
     * @param {string} message 消息内容
     */
    showInfo(message) {
        showNotification(message, 'info');
    }

    /**
     * 销毁应用
     */
    destroy() {
        // 清理定时器
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        
        // 销毁组件
        this.tableManager?.destroy();
        this.historyManager?.clear();
        
        // 清理引用
        this.dataService = null;
        this.tableManager = null;
        this.historyManager = null;
        this.exportManager = null;
        
        this.isInitialized = false;
    }
}

// 当DOM加载完成时初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.jobTracker = new JobApplicationTracker();
});

// 导出主类
window.JobApplicationTracker = JobApplicationTracker;
