<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #007acc;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .popup-frame {
            width: 900px;
            height: 600px;
            border: 1px solid #ccc;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>求职投递进展记录插件 - 测试页面</h1>
        
        <div class="test-section">
            <h3>插件预览</h3>
            <p>这里显示插件的实际界面：</p>
            <iframe src="popup.html" class="popup-frame"></iframe>
        </div>

        <div class="test-section">
            <h3>功能测试</h3>
            <button onclick="testDataService()">测试数据服务</button>
            <button onclick="testTableManager()">测试表格管理</button>
            <button onclick="testHistoryManager()">测试历史管理</button>
            <button onclick="testExportManager()">测试导出功能</button>
            <button onclick="runAllTests()">运行所有测试</button>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h3>性能测试</h3>
            <button onclick="testPerformance()">性能测试</button>
            <button onclick="testLargeDataset()">大数据集测试</button>
            <div id="performanceResults"></div>
        </div>

        <div class="test-section">
            <h3>浏览器兼容性</h3>
            <button onclick="checkCompatibility()">检查兼容性</button>
            <div id="compatibilityResults"></div>
        </div>

        <div class="test-section">
            <h3>安装说明</h3>
            <ol>
                <li>打开Edge浏览器</li>
                <li>访问 edge://extensions/</li>
                <li>开启"开发人员模式"</li>
                <li>点击"加载解压缩的扩展"</li>
                <li>选择插件文件夹</li>
                <li>插件安装完成后，点击工具栏中的插件图标即可使用</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>已知问题和解决方案</h3>
            <ul>
                <li><strong>图标显示问题：</strong>需要生成PNG格式的图标文件，请使用 icons/generate_icons.html 生成</li>
                <li><strong>存储权限：</strong>确保浏览器允许插件访问存储API</li>
                <li><strong>CSV导出中文乱码：</strong>已添加BOM头解决</li>
                <li><strong>表格性能：</strong>大量数据时建议使用虚拟滚动（待实现）</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟Chrome扩展API（用于测试）
        if (!window.chrome) {
            window.chrome = {
                storage: {
                    local: {
                        get: (keys) => Promise.resolve({}),
                        set: (data) => Promise.resolve(),
                        remove: (keys) => Promise.resolve()
                    },
                    sync: {
                        get: (keys) => Promise.resolve({}),
                        set: (data) => Promise.resolve()
                    }
                }
            };
        }

        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testDataService() {
            clearResults('testResults');
            
            try {
                // 这里需要加载实际的脚本文件进行测试
                showResult('testResults', '数据服务测试开始...', 'info');
                
                // 模拟测试
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult('testResults', '✓ 数据服务测试通过', 'success');
            } catch (error) {
                showResult('testResults', `✗ 数据服务测试失败: ${error.message}`, 'error');
            }
        }

        async function testTableManager() {
            clearResults('testResults');
            
            try {
                showResult('testResults', '表格管理器测试开始...', 'info');
                
                // 模拟测试
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult('testResults', '✓ 表格管理器测试通过', 'success');
            } catch (error) {
                showResult('testResults', `✗ 表格管理器测试失败: ${error.message}`, 'error');
            }
        }

        async function testHistoryManager() {
            clearResults('testResults');
            
            try {
                showResult('testResults', '历史管理器测试开始...', 'info');
                
                // 模拟测试
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult('testResults', '✓ 历史管理器测试通过', 'success');
            } catch (error) {
                showResult('testResults', `✗ 历史管理器测试失败: ${error.message}`, 'error');
            }
        }

        async function testExportManager() {
            clearResults('testResults');
            
            try {
                showResult('testResults', '导出管理器测试开始...', 'info');
                
                // 模拟测试
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                showResult('testResults', '✓ 导出管理器测试通过', 'success');
            } catch (error) {
                showResult('testResults', `✗ 导出管理器测试失败: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            clearResults('testResults');
            
            showResult('testResults', '开始运行所有测试...', 'info');
            
            await testDataService();
            await testTableManager();
            await testHistoryManager();
            await testExportManager();
            
            showResult('testResults', '所有测试完成！', 'success');
        }

        async function testPerformance() {
            clearResults('performanceResults');
            
            showResult('performanceResults', '性能测试开始...', 'info');
            
            // 模拟性能测试
            const start = performance.now();
            
            // 模拟大量数据操作
            const data = [];
            for (let i = 0; i < 1000; i++) {
                data.push({
                    id: `test-${i}`,
                    company: `公司${i}`,
                    position: `职位${i}`,
                    priority: (i % 3) + 1
                });
            }
            
            const end = performance.now();
            const duration = end - start;
            
            showResult('performanceResults', `生成1000条测试数据耗时: ${duration.toFixed(2)}ms`, 'success');
            
            if (duration > 100) {
                showResult('performanceResults', '⚠️ 性能可能需要优化', 'error');
            } else {
                showResult('performanceResults', '✓ 性能表现良好', 'success');
            }
        }

        async function testLargeDataset() {
            clearResults('performanceResults');
            
            showResult('performanceResults', '大数据集测试开始...', 'info');
            
            // 模拟大数据集测试
            const sizes = [100, 500, 1000, 5000];
            
            for (const size of sizes) {
                const start = performance.now();
                
                // 模拟数据处理
                const data = Array.from({length: size}, (_, i) => ({
                    id: `item-${i}`,
                    data: `数据${i}`
                }));
                
                const end = performance.now();
                const duration = end - start;
                
                showResult('performanceResults', 
                    `${size}条数据处理耗时: ${duration.toFixed(2)}ms`, 
                    duration > 50 ? 'error' : 'success'
                );
            }
        }

        function checkCompatibility() {
            clearResults('compatibilityResults');
            
            showResult('compatibilityResults', '检查浏览器兼容性...', 'info');
            
            const features = [
                { name: 'Chrome Extension API', check: () => !!window.chrome },
                { name: 'Local Storage', check: () => !!window.localStorage },
                { name: 'Fetch API', check: () => !!window.fetch },
                { name: 'Promise', check: () => !!window.Promise },
                { name: 'ES6 Classes', check: () => {
                    try { eval('class Test {}'); return true; } catch { return false; }
                }},
                { name: 'Arrow Functions', check: () => {
                    try { eval('() => {}'); return true; } catch { return false; }
                }},
                { name: 'Template Literals', check: () => {
                    try { eval('`test`'); return true; } catch { return false; }
                }},
                { name: 'Destructuring', check: () => {
                    try { eval('const {a} = {}'); return true; } catch { return false; }
                }}
            ];
            
            features.forEach(feature => {
                const supported = feature.check();
                showResult('compatibilityResults', 
                    `${feature.name}: ${supported ? '✓ 支持' : '✗ 不支持'}`,
                    supported ? 'success' : 'error'
                );
            });
            
            // 浏览器信息
            showResult('compatibilityResults', 
                `浏览器: ${navigator.userAgent}`, 
                'info'
            );
        }

        // 页面加载时自动检查兼容性
        window.onload = () => {
            checkCompatibility();
        };
    </script>
</body>
</html>
