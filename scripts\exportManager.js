// 导出管理器类

/**
 * 导出管理器类
 */
class ExportManager {
    constructor() {
        this.supportedFormats = ['excel', 'csv'];
    }

    /**
     * 导出数据
     * @param {JobApplication[]} applications 投递记录数组
     * @param {string} format 导出格式 ('excel' | 'csv')
     * @returns {Promise<void>}
     */
    async exportData(applications, format) {
        if (!this.supportedFormats.includes(format)) {
            throw new Error(`不支持的导出格式: ${format}`);
        }

        if (!applications || applications.length === 0) {
            throw new Error('没有数据可导出');
        }

        try {
            let blob;
            let filename;

            switch (format) {
                case 'excel':
                    blob = await this.exportToExcel(applications);
                    filename = `求职投递记录_${this.getDateString()}.xlsx`;
                    break;
                case 'csv':
                    blob = await this.exportToCSV(applications);
                    filename = `求职投递记录_${this.getDateString()}.csv`;
                    break;
                default:
                    throw new Error(`未实现的导出格式: ${format}`);
            }

            this.downloadFile(blob, filename);
            
        } catch (error) {
            console.error('导出失败:', error);
            throw new Error(`导出失败: ${error.message}`);
        }
    }

    /**
     * 导出为Excel格式
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {Promise<Blob>} Excel文件Blob
     */
    async exportToExcel(applications) {
        // 由于浏览器环境限制，这里使用简化的Excel格式（实际上是CSV格式但保存为.xlsx）
        // 在实际项目中，可以使用 SheetJS 等库来生成真正的Excel文件
        
        const csvContent = this.generateCSVContent(applications);
        
        // 创建一个简单的XML格式，模拟Excel文件
        const excelContent = this.createSimpleExcelXML(applications);
        
        return new Blob([excelContent], { 
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
        });
    }

    /**
     * 导出为CSV格式
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {Promise<Blob>} CSV文件Blob
     */
    async exportToCSV(applications) {
        const csvContent = this.generateCSVContent(applications);
        
        // 添加BOM以支持中文
        const BOM = '\uFEFF';
        
        return new Blob([BOM + csvContent], { 
            type: 'text/csv;charset=utf-8' 
        });
    }

    /**
     * 生成CSV内容
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {string} CSV内容字符串
     */
    generateCSVContent(applications) {
        // 定义列标题
        const headers = [
            '公司',
            '职位',
            '投递链接',
            '重视度',
            '行业',
            '标签',
            '地点',
            '进展',
            '状态进度',
            '投递时间',
            '进展时间',
            '备注',
            '内推码',
            '创建时间',
            '更新时间'
        ];

        // 生成CSV行
        const rows = applications.map(app => [
            this.escapeCsvValue(app.company),
            this.escapeCsvValue(app.position),
            this.escapeCsvValue(app.applicationLink),
            app.priority + '级',
            this.escapeCsvValue(app.industry),
            this.escapeCsvValue(Array.isArray(app.tags) ? app.tags.join(', ') : ''),
            this.escapeCsvValue(app.location),
            this.escapeCsvValue(app.progress),
            this.escapeCsvValue(app.statusProgress),
            formatDate(app.applicationTime),
            formatDate(app.progressTime),
            this.escapeCsvValue(app.notes),
            this.escapeCsvValue(app.referralCode),
            formatDateTime(app.createdAt),
            formatDateTime(app.updatedAt)
        ]);

        // 组合CSV内容
        const csvLines = [headers, ...rows];
        return csvLines.map(row => row.join(',')).join('\n');
    }

    /**
     * 转义CSV值
     * @param {any} value 要转义的值
     * @returns {string} 转义后的值
     */
    escapeCsvValue(value) {
        if (value == null) return '';
        
        const stringValue = String(value);
        
        // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
            return '"' + stringValue.replace(/"/g, '""') + '"';
        }
        
        return stringValue;
    }

    /**
     * 创建简单的Excel XML格式
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {string} Excel XML内容
     */
    createSimpleExcelXML(applications) {
        const headers = [
            '公司', '职位', '投递链接', '重视度', '行业', '标签', '地点',
            '进展', '状态进度', '投递时间', '进展时间', '备注', '内推码',
            '创建时间', '更新时间'
        ];

        let xml = `<?xml version="1.0" encoding="UTF-8"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:o="urn:schemas-microsoft-com:office:office"
 xmlns:x="urn:schemas-microsoft-com:office:excel"
 xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
 xmlns:html="http://www.w3.org/TR/REC-html40">
 <Worksheet ss:Name="求职投递记录">
  <Table>`;

        // 添加标题行
        xml += '<Row>';
        headers.forEach(header => {
            xml += `<Cell><Data ss:Type="String">${this.escapeXml(header)}</Data></Cell>`;
        });
        xml += '</Row>';

        // 添加数据行
        applications.forEach(app => {
            xml += '<Row>';
            
            const values = [
                app.company,
                app.position,
                app.applicationLink,
                app.priority + '级',
                app.industry,
                Array.isArray(app.tags) ? app.tags.join(', ') : '',
                app.location,
                app.progress,
                app.statusProgress,
                formatDate(app.applicationTime),
                formatDate(app.progressTime),
                app.notes,
                app.referralCode,
                formatDateTime(app.createdAt),
                formatDateTime(app.updatedAt)
            ];

            values.forEach(value => {
                xml += `<Cell><Data ss:Type="String">${this.escapeXml(String(value || ''))}</Data></Cell>`;
            });
            
            xml += '</Row>';
        });

        xml += `  </Table>
 </Worksheet>
</Workbook>`;

        return xml;
    }

    /**
     * 转义XML特殊字符
     * @param {string} text 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeXml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * 下载文件
     * @param {Blob} blob 文件Blob
     * @param {string} filename 文件名
     */
    downloadFile(blob, filename) {
        try {
            // 创建下载链接
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            
            // 添加到页面并触发下载
            document.body.appendChild(link);
            link.click();
            
            // 清理
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
        } catch (error) {
            console.error('下载文件失败:', error);
            throw new Error('下载文件失败');
        }
    }

    /**
     * 获取当前日期字符串
     * @returns {string} 日期字符串 YYYYMMDD
     */
    getDateString() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        return `${year}${month}${day}`;
    }

    /**
     * 获取支持的导出格式
     * @returns {string[]} 支持的格式数组
     */
    getSupportedFormats() {
        return [...this.supportedFormats];
    }

    /**
     * 验证导出数据
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {boolean} 是否有效
     */
    validateExportData(applications) {
        if (!Array.isArray(applications)) {
            return false;
        }

        if (applications.length === 0) {
            return false;
        }

        // 检查必要字段
        return applications.every(app => 
            app && 
            typeof app === 'object' && 
            app.id && 
            app.company !== undefined && 
            app.position !== undefined
        );
    }

    /**
     * 获取导出统计信息
     * @param {JobApplication[]} applications 投递记录数组
     * @returns {Object} 统计信息
     */
    getExportStats(applications) {
        if (!this.validateExportData(applications)) {
            return null;
        }

        const stats = {
            totalRecords: applications.length,
            companies: new Set(applications.map(app => app.company)).size,
            positions: new Set(applications.map(app => app.position)).size,
            dateRange: {
                earliest: null,
                latest: null
            },
            progressStats: {},
            priorityStats: { 1: 0, 2: 0, 3: 0 }
        };

        // 计算日期范围
        const dates = applications
            .map(app => new Date(app.applicationTime))
            .filter(date => !isNaN(date.getTime()))
            .sort((a, b) => a - b);

        if (dates.length > 0) {
            stats.dateRange.earliest = formatDate(dates[0]);
            stats.dateRange.latest = formatDate(dates[dates.length - 1]);
        }

        // 计算进展统计
        applications.forEach(app => {
            // 进展统计
            if (app.progress) {
                stats.progressStats[app.progress] = (stats.progressStats[app.progress] || 0) + 1;
            }

            // 重视度统计
            if (app.priority >= 1 && app.priority <= 3) {
                stats.priorityStats[app.priority]++;
            }
        });

        return stats;
    }

    /**
     * 生成导出预览
     * @param {JobApplication[]} applications 投递记录数组
     * @param {number} maxRows 最大预览行数
     * @returns {Object} 预览数据
     */
    generatePreview(applications, maxRows = 5) {
        if (!this.validateExportData(applications)) {
            return null;
        }

        const previewData = applications.slice(0, maxRows);
        const stats = this.getExportStats(applications);

        return {
            preview: previewData,
            stats: stats,
            hasMore: applications.length > maxRows,
            totalRows: applications.length
        };
    }
}

// 导出类
window.ExportManager = ExportManager;
