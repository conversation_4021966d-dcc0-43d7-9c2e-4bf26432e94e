/* 主页面样式文件 - 适用于新标签页显示 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f5;
    min-height: 100vh;
    overflow-x: auto;
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 页面标题 */
.page-header {
    background: linear-gradient(135deg, #007acc 0%, #005a9e 100%);
    color: white;
    padding: 20px 24px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.page-header p {
    font-size: 14px;
    opacity: 0.9;
}

/* 工具栏样式 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 12px;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    white-space: nowrap;
    min-height: 40px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary {
    background: #007acc;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #005a9e;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-icon {
    padding: 10px;
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
    min-width: 40px;
}

.btn-icon:hover:not(:disabled) {
    background: #e9ecef;
    color: #495057;
}

.icon {
    font-size: 16px;
    line-height: 1;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 24px;
    background: white;
    overflow: hidden;
}

/* 表格容器 */
.table-container {
    flex: 1;
    overflow: auto;
    margin: 16px 0;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

/* 底部工具栏 */
.bottom-toolbar {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    flex-wrap: wrap;
}

/* 统计信息栏 */
.stats-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    font-size: 13px;
    color: #6c757d;
    flex-wrap: wrap;
    gap: 12px;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.stats-value {
    font-weight: 600;
    color: #007acc;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-width: 500px;
    width: 90%;
    max-height: 80%;
    overflow: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.modal-close:hover {
    background: #f8f9fa;
    color: #495057;
}

.modal-body {
    padding: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    max-width: 350px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: #28a745;
}

.notification-error {
    background: #dc3545;
}

.notification-warning {
    background: #ffc107;
    color: #212529;
}

.notification-info {
    background: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        margin: 0;
        box-shadow: none;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 16px 20px;
    }
    
    .page-header h1 {
        font-size: 20px;
    }
    
    .toolbar {
        padding: 12px 20px;
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-left,
    .toolbar-right {
        justify-content: center;
    }
    
    .main-content {
        padding: 0 20px;
    }
    
    .btn {
        padding: 8px 16px;
        font-size: 13px;
        min-height: 36px;
    }
    
    .bottom-toolbar {
        padding: 12px 20px;
        flex-direction: column;
    }
    
    .stats-bar {
        padding: 10px 20px;
        flex-direction: column;
        align-items: stretch;
        text-align: center;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
}

@media (max-width: 480px) {
    .toolbar-left,
    .toolbar-right {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007acc;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state .icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 12px;
    color: #495057;
    font-size: 18px;
}

.empty-state p {
    margin-bottom: 24px;
    font-size: 14px;
}

/* 焦点样式 */
.btn:focus,
button:focus,
input:focus,
select:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
}

/* 打印样式 */
@media print {
    .toolbar,
    .bottom-toolbar,
    .stats-bar,
    .modal {
        display: none !important;
    }
    
    .container {
        box-shadow: none;
        max-width: none;
    }
    
    .table-container {
        border: none;
        overflow: visible;
    }
}
