<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成插件图标</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .icon {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #ccc;
        }
        canvas {
            border: 1px solid #ccc;
        }
        button {
            background: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a9e;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>求职投递进展记录插件 - 图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击"生成图标"按钮生成不同尺寸的图标</li>
                <li>右键点击每个图标，选择"另存为"</li>
                <li>保存时使用对应的文件名：icon16.png, icon32.png, icon48.png, icon128.png</li>
                <li>将保存的图标文件放入插件的 icons 目录中</li>
            </ol>
        </div>

        <button onclick="generateIcons()">生成图标</button>
        <button onclick="downloadAll()">下载所有图标</button>

        <div class="icon-preview" id="iconPreview">
            <!-- 图标将在这里生成 -->
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 128;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景圆形
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 4*scale, 0, 2 * Math.PI);
            ctx.fillStyle = '#007acc';
            ctx.fill();
            ctx.strokeStyle = '#005a9e';
            ctx.lineWidth = 2*scale;
            ctx.stroke();
            
            // 文档矩形
            const docWidth = 48*scale;
            const docHeight = 64*scale;
            const docX = (size - docWidth) / 2;
            const docY = 24*scale;
            
            ctx.fillStyle = 'white';
            ctx.fillRect(docX, docY, docWidth, docHeight);
            ctx.strokeStyle = '#007acc';
            ctx.lineWidth = 2*scale;
            ctx.strokeRect(docX, docY, docWidth, docHeight);
            
            // 文档内容线条
            ctx.strokeStyle = '#007acc';
            ctx.lineWidth = 2*scale;
            ctx.lineCap = 'round';
            
            const lines = [
                [docX + 8*scale, docY + 12*scale, docX + 40*scale, docY + 12*scale],
                [docX + 8*scale, docY + 20*scale, docX + 36*scale, docY + 20*scale],
                [docX + 8*scale, docY + 28*scale, docX + 38*scale, docY + 28*scale],
                [docX + 8*scale, docY + 36*scale, docX + 33*scale, docY + 36*scale],
                [docX + 8*scale, docY + 44*scale, docX + 40*scale, docY + 44*scale],
                [docX + 8*scale, docY + 52*scale, docX + 34*scale, docY + 52*scale]
            ];
            
            lines.forEach(line => {
                ctx.beginPath();
                ctx.moveTo(line[0], line[1]);
                ctx.lineTo(line[2], line[3]);
                ctx.stroke();
            });
            
            // 进度指示器（绿色圆圈和勾选）
            if (size >= 32) {
                const checkX = size - 24*scale;
                const checkY = 40*scale;
                const checkRadius = 12*scale;
                
                ctx.beginPath();
                ctx.arc(checkX, checkY, checkRadius, 0, 2 * Math.PI);
                ctx.fillStyle = '#28a745';
                ctx.fill();
                
                // 勾选标记
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2*scale;
                ctx.lineCap = 'round';
                ctx.lineJoin = 'round';
                
                ctx.beginPath();
                ctx.moveTo(checkX - 6*scale, checkY);
                ctx.lineTo(checkX - 2*scale, checkY + 4*scale);
                ctx.lineTo(checkX + 6*scale, checkY - 4*scale);
                ctx.stroke();
            }
            
            // 文字（仅在较大尺寸显示）
            if (size >= 48) {
                ctx.fillStyle = 'white';
                ctx.font = `bold ${12*scale}px Arial, sans-serif`;
                ctx.textAlign = 'center';
                ctx.fillText('求职', size/2, size - 20*scale);
            }
        }

        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';
            
            sizes.forEach(size => {
                const container = document.createElement('div');
                container.className = 'icon-item';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.className = 'icon';
                
                drawIcon(canvas, size);
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = '下载';
                downloadBtn.onclick = () => downloadIcon(canvas, `icon${size}.png`);
                
                container.appendChild(canvas);
                container.appendChild(label);
                container.appendChild(downloadBtn);
                preview.appendChild(container);
            });
        }

        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        function downloadAll() {
            const canvases = document.querySelectorAll('.icon');
            const sizes = [16, 32, 48, 128];
            
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadIcon(canvas, `icon${sizes[index]}.png`);
                }, index * 500);
            });
        }

        // 页面加载时自动生成图标
        window.onload = generateIcons;
    </script>
</body>
</html>
