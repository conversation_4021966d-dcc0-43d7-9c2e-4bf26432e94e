// 历史记录管理器类

/**
 * 操作类型枚举
 */
const ActionType = {
    ADD: 'add',
    UPDATE: 'update',
    DELETE: 'delete',
    MOVE: 'move',
    INSERT: 'insert',
    BATCH_DELETE: 'batch_delete',
    REORDER: 'reorder'
};

/**
 * 操作记录类
 */
class Action {
    constructor(type, data, timestamp = new Date()) {
        this.type = type;
        this.data = data;
        this.timestamp = timestamp;
        this.id = generateId();
    }

    /**
     * 创建反向操作
     * @returns {Action} 反向操作
     */
    reverse() {
        switch (this.type) {
            case ActionType.ADD:
                return new Action(ActionType.DELETE, {
                    id: this.data.application.id
                });

            case ActionType.DELETE:
                return new Action(ActionType.ADD, {
                    application: this.data.application,
                    index: this.data.index
                });

            case ActionType.UPDATE:
                return new Action(ActionType.UPDATE, {
                    id: this.data.id,
                    oldValues: this.data.newValues,
                    newValues: this.data.oldValues
                });

            case ActionType.BATCH_DELETE:
                return new Action(ActionType.ADD, {
                    applications: this.data.applications,
                    indices: this.data.indices
                });

            case ActionType.MOVE:
                return new Action(ActionType.MOVE, {
                    ids: this.data.ids,
                    fromIndices: this.data.toIndices,
                    toIndices: this.data.fromIndices
                });

            case ActionType.INSERT:
                return new Action(ActionType.DELETE, {
                    id: this.data.application.id
                });

            case ActionType.REORDER:
                return new Action(ActionType.REORDER, {
                    oldOrder: this.data.newOrder,
                    newOrder: this.data.oldOrder
                });

            default:
                throw new Error(`未知的操作类型: ${this.type}`);
        }
    }

    /**
     * 获取操作描述
     * @returns {string} 操作描述
     */
    getDescription() {
        switch (this.type) {
            case ActionType.ADD:
                return `添加记录: ${this.data.application?.company || '未知公司'}`;
            case ActionType.DELETE:
                return `删除记录: ${this.data.application?.company || '未知公司'}`;
            case ActionType.UPDATE:
                return `更新记录: ${this.data.application?.company || '未知公司'}`;
            case ActionType.BATCH_DELETE:
                return `批量删除 ${this.data.applications?.length || 0} 条记录`;
            case ActionType.MOVE:
                return `移动 ${this.data.ids?.length || 0} 条记录`;
            case ActionType.INSERT:
                return `插入记录: ${this.data.application?.company || '未知公司'}`;
            case ActionType.REORDER:
                return '重新排序记录';
            default:
                return '未知操作';
        }
    }
}

/**
 * 历史记录管理器类
 */
class HistoryManager {
    constructor(maxHistorySize = 50) {
        this.maxHistorySize = maxHistorySize;
        this.undoStack = [];
        this.redoStack = [];
        this.isExecuting = false;
    }

    /**
     * 记录操作
     * @param {string} type 操作类型
     * @param {any} data 操作数据
     */
    recordAction(type, data) {
        if (this.isExecuting) {
            return; // 防止在执行撤销/重做时记录操作
        }

        const action = new Action(type, data);
        
        // 添加到撤销栈
        this.undoStack.push(action);
        
        // 清空重做栈
        this.redoStack = [];
        
        // 限制历史记录大小
        if (this.undoStack.length > this.maxHistorySize) {
            this.undoStack.shift();
        }

        // 触发历史状态变更事件
        this.dispatchHistoryChanged();
    }

    /**
     * 撤销操作
     * @returns {Action|null} 被撤销的操作
     */
    async undo() {
        if (!this.canUndo()) {
            return null;
        }

        this.isExecuting = true;
        
        try {
            const action = this.undoStack.pop();
            const reverseAction = action.reverse();
            
            // 执行反向操作
            await this.executeAction(reverseAction);
            
            // 添加到重做栈
            this.redoStack.push(action);
            
            // 触发历史状态变更事件
            this.dispatchHistoryChanged();
            
            return action;
            
        } catch (error) {
            console.error('撤销操作失败:', error);
            // 如果撤销失败，将操作放回撤销栈
            this.undoStack.push(action);
            throw error;
        } finally {
            this.isExecuting = false;
        }
    }

    /**
     * 重做操作
     * @returns {Action|null} 被重做的操作
     */
    async redo() {
        if (!this.canRedo()) {
            return null;
        }

        this.isExecuting = true;
        
        try {
            const action = this.redoStack.pop();
            
            // 执行原操作
            await this.executeAction(action);
            
            // 添加到撤销栈
            this.undoStack.push(action);
            
            // 触发历史状态变更事件
            this.dispatchHistoryChanged();
            
            return action;
            
        } catch (error) {
            console.error('重做操作失败:', error);
            // 如果重做失败，将操作放回重做栈
            this.redoStack.push(action);
            throw error;
        } finally {
            this.isExecuting = false;
        }
    }

    /**
     * 执行操作
     * @param {Action} action 要执行的操作
     */
    async executeAction(action) {
        // 这个方法需要在使用时注入具体的执行逻辑
        if (this.actionExecutor) {
            await this.actionExecutor(action);
        } else {
            throw new Error('未设置操作执行器');
        }
    }

    /**
     * 设置操作执行器
     * @param {Function} executor 执行器函数
     */
    setActionExecutor(executor) {
        this.actionExecutor = executor;
    }

    /**
     * 是否可以撤销
     * @returns {boolean} 是否可以撤销
     */
    canUndo() {
        return this.undoStack.length > 0 && !this.isExecuting;
    }

    /**
     * 是否可以重做
     * @returns {boolean} 是否可以重做
     */
    canRedo() {
        return this.redoStack.length > 0 && !this.isExecuting;
    }

    /**
     * 清空历史记录
     */
    clear() {
        this.undoStack = [];
        this.redoStack = [];
        this.dispatchHistoryChanged();
    }

    /**
     * 获取撤销栈大小
     * @returns {number} 撤销栈大小
     */
    getUndoStackSize() {
        return this.undoStack.length;
    }

    /**
     * 获取重做栈大小
     * @returns {number} 重做栈大小
     */
    getRedoStackSize() {
        return this.redoStack.length;
    }

    /**
     * 获取最近的操作
     * @param {number} count 获取数量
     * @returns {Action[]} 最近的操作数组
     */
    getRecentActions(count = 10) {
        return this.undoStack.slice(-count).reverse();
    }

    /**
     * 获取历史记录摘要
     * @returns {Object} 历史记录摘要
     */
    getSummary() {
        return {
            undoCount: this.undoStack.length,
            redoCount: this.redoStack.length,
            canUndo: this.canUndo(),
            canRedo: this.canRedo(),
            lastAction: this.undoStack.length > 0 ? this.undoStack[this.undoStack.length - 1] : null,
            nextRedoAction: this.redoStack.length > 0 ? this.redoStack[this.redoStack.length - 1] : null
        };
    }

    /**
     * 分发历史状态变更事件
     */
    dispatchHistoryChanged() {
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('historyChanged', {
                detail: this.getSummary()
            });
            window.dispatchEvent(event);
        }
    }

    /**
     * 创建操作数据的辅助方法
     */
    static createActionData = {
        /**
         * 创建添加操作数据
         * @param {JobApplication} application 应用记录
         * @param {number} index 插入位置
         * @returns {Object} 操作数据
         */
        add(application, index = 0) {
            return { application: deepClone(application), index };
        },

        /**
         * 创建删除操作数据
         * @param {JobApplication} application 应用记录
         * @param {number} index 原位置
         * @returns {Object} 操作数据
         */
        delete(application, index) {
            return { application: deepClone(application), index };
        },

        /**
         * 创建更新操作数据
         * @param {string} id 记录ID
         * @param {Object} oldValues 旧值
         * @param {Object} newValues 新值
         * @returns {Object} 操作数据
         */
        update(id, oldValues, newValues) {
            return { 
                id, 
                oldValues: deepClone(oldValues), 
                newValues: deepClone(newValues) 
            };
        },

        /**
         * 创建批量删除操作数据
         * @param {JobApplication[]} applications 应用记录数组
         * @param {number[]} indices 原位置数组
         * @returns {Object} 操作数据
         */
        batchDelete(applications, indices) {
            return { 
                applications: applications.map(app => deepClone(app)), 
                indices: [...indices] 
            };
        },

        /**
         * 创建移动操作数据
         * @param {string[]} ids 记录ID数组
         * @param {number[]} fromIndices 原位置数组
         * @param {number[]} toIndices 新位置数组
         * @returns {Object} 操作数据
         */
        move(ids, fromIndices, toIndices) {
            return { 
                ids: [...ids], 
                fromIndices: [...fromIndices], 
                toIndices: [...toIndices] 
            };
        },

        /**
         * 创建重新排序操作数据
         * @param {string[]} oldOrder 旧顺序
         * @param {string[]} newOrder 新顺序
         * @returns {Object} 操作数据
         */
        reorder(oldOrder, newOrder) {
            return { 
                oldOrder: [...oldOrder], 
                newOrder: [...newOrder] 
            };
        }
    };
}

// 导出类和常量
window.HistoryManager = HistoryManager;
window.ActionType = ActionType;
window.Action = Action;
