// 背景脚本 - 处理插件的生命周期事件

// 插件安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
    console.log('求职投递进展记录插件已安装');
    
    // 初始化默认设置
    chrome.storage.sync.set({
        settings: {
            priorityColors: { 
                1: '#ff4444', 
                2: '#ffaa00', 
                3: '#44ff44' 
            },
            defaultView: 'table',
            autoSave: true
        }
    });
});

// 处理插件图标点击事件
chrome.action.onClicked.addListener((tab) => {
    // 打开弹出窗口（这个事件在有default_popup时不会触发）
    console.log('插件图标被点击');
});

// 监听存储变化
chrome.storage.onChanged.addListener((changes, namespace) => {
    console.log('存储数据发生变化:', changes);
});

// 处理错误
chrome.runtime.onSuspend.addListener(() => {
    console.log('插件即将被挂起');
});
