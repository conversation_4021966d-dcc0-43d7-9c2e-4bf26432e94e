/* 表格专用样式 */

.applications-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 13px;
    margin-top: 8px;
}

.applications-table th,
.applications-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    vertical-align: middle;
}

.applications-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
}

.applications-table tbody tr {
    transition: background-color 0.2s ease;
}

.applications-table tbody tr:hover {
    background: #f8f9fa;
}

.applications-table tbody tr.selected {
    background: #e3f2fd;
}

/* 列宽设置 */
.checkbox-col {
    width: 40px;
    text-align: center;
}

.applications-table th:nth-child(2) { /* 公司 */
    width: 120px;
}

.applications-table th:nth-child(3) { /* 职位 */
    width: 120px;
}

.applications-table th:nth-child(4) { /* 重视度 */
    width: 80px;
}

.applications-table th:nth-child(5) { /* 进展 */
    width: 100px;
}

.applications-table th:nth-child(6) { /* 状态 */
    width: 100px;
}

.applications-table th:nth-child(7) { /* 投递时间 */
    width: 110px;
}

.applications-table th:nth-child(8) { /* 进展时间 */
    width: 110px;
}

.applications-table th:nth-child(9) { /* 操作 */
    width: 80px;
}

/* 可排序列标题 */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable:hover {
    background: #e9ecef;
}

.sortable::after {
    content: '↕';
    position: absolute;
    right: 8px;
    opacity: 0.5;
    font-size: 12px;
}

.sortable.sort-asc::after {
    content: '↑';
    opacity: 1;
    color: #007acc;
}

.sortable.sort-desc::after {
    content: '↓';
    opacity: 1;
    color: #007acc;
}

/* 复选框样式 */
.table-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

/* 重视度颜色标识 */
.priority-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 6px;
    vertical-align: middle;
}

.priority-1 .priority-indicator {
    background: #ff4444;
}

.priority-2 .priority-indicator {
    background: #ffaa00;
}

.priority-3 .priority-indicator {
    background: #44ff44;
}

/* 内联编辑样式 */
.editable {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    min-height: 20px;
    display: block;
}

.editable:hover {
    background: #f0f0f0;
}

.editable.editing {
    background: white;
    border: 2px solid #007acc;
    cursor: text;
}

.edit-input {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-size: inherit;
    font-family: inherit;
    padding: 0;
}

.edit-select {
    width: 100%;
    border: none;
    outline: none;
    background: white;
    font-size: inherit;
    font-family: inherit;
    padding: 2px 4px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

/* 下拉选择器样式 */
.select-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
}

.custom-select {
    appearance: none;
    width: 100%;
    padding: 4px 24px 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    font-size: inherit;
    cursor: pointer;
}

.custom-select:focus {
    border-color: #007acc;
    outline: none;
}

.select-wrapper::after {
    content: '▼';
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    font-size: 10px;
    color: #666;
}

/* 操作按钮 */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    color: #6c757d;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.action-btn.edit {
    color: #007acc;
}

.action-btn.delete {
    color: #dc3545;
}

.action-btn.edit:hover {
    background: #e3f2fd;
}

.action-btn.delete:hover {
    background: #f8d7da;
}

/* 标签样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    max-width: 150px;
}

.tag {
    display: inline-block;
    padding: 2px 6px;
    background: #e9ecef;
    color: #495057;
    border-radius: 12px;
    font-size: 11px;
    white-space: nowrap;
}

.tag.editable {
    cursor: pointer;
}

.tag.editable:hover {
    background: #dee2e6;
}

/* 链接样式 */
.application-link {
    color: #007acc;
    text-decoration: none;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
}

.application-link:hover {
    text-decoration: underline;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.empty-state .icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 8px;
    color: #495057;
}

.empty-state p {
    margin-bottom: 20px;
}

/* 加载状态 */
.table-loading {
    text-align: center;
    padding: 40px 20px;
}

/* 响应式表格 */
@media (max-width: 768px) {
    .applications-table {
        font-size: 12px;
    }
    
    .applications-table th,
    .applications-table td {
        padding: 6px 8px;
    }
    
    /* 隐藏部分列在小屏幕上 */
    .applications-table th:nth-child(n+7),
    .applications-table td:nth-child(n+7) {
        display: none;
    }
}

/* 表格行拖拽样式 */
.row-dragging {
    opacity: 0.5;
    background: #f8f9fa;
}

.drop-indicator {
    height: 2px;
    background: #007acc;
    margin: 0;
    padding: 0;
}

.drop-indicator td {
    padding: 0;
    border: none;
}
